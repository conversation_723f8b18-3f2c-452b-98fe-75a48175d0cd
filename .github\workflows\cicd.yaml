name: binance-trade-bot
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - "*"

jobs:
  Lint:
    runs-on: ubuntu-20.04
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.7
    - id: changed-files
      name: Get Changed Files
      uses: dorny/paths-filter@v2
      with:
        token: ${{ github.token }}
        list-files: shell
        filters: |
          repo:
            - added|modified:
              - '**'
    - name: Set Cache Key
      run: echo "PY=$(python --version --version | sha256sum | cut -d' ' -f1)" >> $GITHUB_ENV
    - uses: actions/cache@v2
      with:
        path: ~/.cache/pre-commit
        key: pre-commit|${{ env.PY }}|${{ hashFiles('.pre-commit-config.yaml') }}
    - name: Check ALL Files On Branch
      uses: pre-commit/action@v2.0.0
      if: github.event_name != 'pull_request'
    - name: Check Changed Files On PR
      uses: pre-commit/action@v2.0.0
      if: github.event_name == 'pull_request'
      with:
        extra_args: --files ${{ steps.changed-files.outputs.repo_files }}

  Docker:
    runs-on: ubuntu-latest
    needs: Lint
    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Login to DockerHub
        if: github.event_name == 'push'
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          platforms: linux/amd64,linux/arm64,linux/arm/v6,linux/arm/v7
          push: ${{ github.event_name == 'push' }}
          tags: ${{ github.repository }}:latest
      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
